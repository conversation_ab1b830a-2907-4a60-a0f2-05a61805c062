<script lang="ts">
	import type {
		Breadcrumb,
		RepairPart,
		SelectedPart,
		RepairSymptom,
		RepairProcess,
		RepairGrade
	} from '$lib/types/types';

	import { onMount, tick } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { authClient } from '$lib/services/AxiosBackend';
	import { isRePrint } from '$lib/utils/BarcodeUtils';
	import { REPAIR_STATUS_WAITING } from '$stores/repairStore';
	import { refreshWorkHistory } from '$lib/stores/workHistoryStore';
	import { openWebviewWindow } from '$lib/services/windowService';

	import {
		addMemo,
		executeMessage,
		formattedReturnReason,
		generateRandomNumber,
		handleCatch,
		preventKoreanInput,
		processBarcode
	} from '$lib/Functions';
	import { AudioEvent, playAudio, initAudio } from '$lib/utils/AudioManager';
	import { errMessages } from '$lib/Messages';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import ScanMessage from '$components/UI/ScanMessage.svelte';
	import ExampleBarcodeCommand from '$components/Snippets/ExampleBarcodeCommand.svelte';
	import MessageModal from '$components/UI/MessageModal.svelte';
	import GuestSearch from '$components/Snippets/GuestSearch.svelte';
	import WorkHistoryList from '$components/WorkHistory/WorkHistoryList.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faBarcode } from '@fortawesome/free-solid-svg-icons/faBarcode';
	import { faBan } from '@fortawesome/free-solid-svg-icons/faBan';
	import { faBoxesPacking } from '@fortawesome/free-solid-svg-icons/faBoxesPacking';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faMinus } from '@fortawesome/free-solid-svg-icons/faMinus';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faCopyright } from '@fortawesome/free-regular-svg-icons/faCopyright';
	import { faQuestion } from '@fortawesome/free-solid-svg-icons/faQuestion';

	const user: User = getUser();
	const apiUrl = '/api/wms/repairs';
	let isLoading = $state(false); // 로딩중 표시 여부

	// 필수 변수 세팅 시작==================
	let showGradeContent = $state(false); // 수리 등급 (새로운 첫 번째 단계)
	let showSymptomContent = $state(false); // 증상 내용 (두 번째 단계)
	let showProcessContent = $state(false); // 처리 내용 (세 번째 단계)
	let showPartsContentInternal = $state(false); // 구성품 추가 (내부 상태)
	let showPartsContent = $derived(
		// 게스트 사용자는 구성품 추가 기능을 사용할 수 없음
		user.role.toLowerCase() !== 'guest' && showPartsContentInternal
	);

	let prodScanMessage = $state('');

	let checkingBarcode = $state('');
	let checkingProduct: any = $state(null);
	let selectedGradeCode = $state(''); // 수리 등급 (새로운 첫 번째 선택)
	let selectedSymptomCode = $state(''); // 증상 내용 (두 번째 선택)
	let selectedProcessCode = $state(''); // 처리 내용 (세 번째 선택)
	let repairStatus = $state('complete');
	let osReinstall = $derived(selectedProcessCode === 'RP_SW');
	let isAppleProduct = $derived(checkingProduct?.req?.req_type === 2);
	let reqType = $derived(checkingProduct?.req?.req_type);
	let memo = $state('');

	// 변수::입력 포커스, 읽기 전용, 메시지 표시, 버튼 활성화 변수
	let focusCheckingBarcode: HTMLInputElement; // 바코드 스캔 input 포커싱
	let isCompleteButtonSuccess = $derived(!!selectedProcessCode); // 점검완료 버튼 활성화 상태 (처리 내용이 마지막 단계)
	let commandVisible = $state(false); // 명령어 바코드 표시 여부
	// 필수 변수 세팅 종료==================

	// OS 재설치 체크박스 표시 조건
	let isOsReinstallAllowed = $derived(!selectedGradeCode.startsWith('ST_XL')); // 수리 등급 조건
	let isComputerCategory = $derived(
		checkingProduct?.cate4.name === '컴퓨터' &&
			['미니PC', '노트북', '브랜드PC', '일체형PC', '조립PC', '컴퓨터'].includes(
				checkingProduct?.cate5.name
			)
	); // 컴퓨터 카테고리 조건
	let isTabletCategory = $derived(
		checkingProduct?.cate4.name === '태블릿PC/액세서리' &&
			checkingProduct?.cate5.name === '태블릿PC'
	); // 태블릿 카테고리 조건

	// 모달창 관련 변수
	let modal: MessageModal;
	let modalType = $state<'error' | 'warning' | 'success'>('error');
	let modalTitle = $state('');
	let modalMessage = $state('');

	// 모달창 표시 함수
	function showModal(
		message: string,
		type: 'error' | 'warning' | 'success' = 'error',
		title: string = '알림'
	) {
		modalType = type;
		modalTitle = title;
		modalMessage = message;

		if (modal) {
			modal.show();
		} else {
			console.error('modal이 undefined입니다. 컴포넌트가 아직 마운트되지 않았을 수 있습니다.');
			// 대안으로 alert 사용 (임시)
			executeMessage(`${title}: ${message}`);
		}
	}

	// 모달 닫힘 핸들러
	function handleModalClose() {
		setTimeout(() => {
			if (focusCheckingBarcode) {
				checkView();
				focusCheckingBarcode.focus();
				console.log('모달 닫힌 후 포커스 재설정');
			}
		}, 100);
	}

	// 게스트 회원에게 숨겨야 하는 부분들 시작========
	let showRestrictedArea = $state(true);

	function setGuestUser() {
		if (user.role.toLowerCase() === 'guest') {
			showRestrictedArea = false;
		}
	}
	// 게스트 회원에게 숨겨야 하는 부분들 종료========

	// 에러 발생 시 true 반환
	function handleError(message: string) {
		prodScanMessage = message;

		checkView();
		return true;
	}

	async function activeFocus() {
		checkingBarcode = '';

		// DOM 요소가 존재하는지 확인
		if (focusCheckingBarcode) {
			focusCheckingBarcode.value = '';
		}

		await tick();

		// 포커스 설정 전에 다시 확인
		if (focusCheckingBarcode) {
			try {
				focusCheckingBarcode.focus();
				console.log('포커스 설정 성공');

				// 추가 안전장치: requestAnimationFrame으로 한 번 더 시도
				requestAnimationFrame(() => {
					if (focusCheckingBarcode && document.activeElement !== focusCheckingBarcode) {
						focusCheckingBarcode.focus();
						console.log('requestAnimationFrame으로 포커스 재설정');
					}
				});
			} catch (error) {
				console.error('포커스 설정 실패:', error);
			}
		} else {
			console.error('focusCheckingBarcode가 null입니다');
		}
	}

	function checkView() {
		playAudio(AudioEvent.SCAN_BARCODE); // 오디오: 바코드를 스캔해 주시기 바랍니다.

		if (checkingProduct && checkingProduct.id) {
			showGradeContent = true;

			// 이미 선택된 값이 있으면 해당 필드까지 표시
			if (selectedSymptomCode) showSymptomContent = true;
			if (selectedProcessCode) showProcessContent = true;
			// @todo: 구성품 시스템이 완성 될 때까지 사용 안 함
			// if (selectedGradeCode && selectedPartsList.length > 0) {
			// 	showPartsContentInternal = true;
			// }
		} else {
			// 상품 정보가 없으면 모든 필드 숨김
			showGradeContent = false;
			showSymptomContent = false;
			showProcessContent = false;
			showPartsContentInternal = false;
		}

		osReinstall = false;
		activeFocus();
	}

	/**
	 * 작업간 모두 바코드를 이용해 처리한다.
	 * 바코드를 찍으면 실제 이 곳에서 분기하여 각각 처리
	 */
	const handleBarcodeInput = async (event: Event) => {
		event.preventDefault();

		const barcode = checkingBarcode.trim();
		if (barcode === '') {
			prodScanMessage = '바코드를 스캔(입력)해 주시기 바랍니다.';
			await activeFocus();
			return;
		}

		let prefixes = ['grade/', 'check/', 'repair/']; // grade/를 첫 번째로 변경

		if (prefixes.some((prefix) => barcode.startsWith(prefix))) {
			selectProcess(barcode);
		} else if (barcode.startsWith('memo/')) {
			const code = barcode.split('/')[1];
			if (code) {
				memo = checkingProduct?.repair_product?.memo ?? '';
				memo = addMemo(code, memo);
			} else {
				handleError(errMessages.memo_code);
			}
			await activeFocus();
		} else if (barcode === 'complete' || barcode === 'waiting') {
			await completeCheckIn();
		} else if (barcode.startsWith('cancel')) {
			await cancelCheckIn();
		} else if (barcode === 'print/qaid') {
			// QAID 재발행
			if (checkingProduct && checkingProduct.qaid) {
				const qaid = checkingProduct.qaid;
				const id = checkingProduct.id;
				await isRePrint(qaid, id);
			} else {
				handleError(errMessages.no_qaid);
			}
		} else {
			checkingBarcode = processBarcode(barcode);
			await getProductInfo();
		}
	};

	let repairSymptomCodes: RepairSymptom[] = $state([]);
	let repairProcessCodes: RepairProcess[] = $state([]);
	let repairGradeCodes: RepairGrade[] = $state([]);

	// API에서 가져온 전체 수리 부품 목록
	let repairParts: RepairPart[] = $state([]);
	let repairPartsCategories: any[] = $state([]);

	// 작업자가 선택한 부품(parts)의 ID, 임시 저장
	let selectedParts = $state('');
	let selectedPartsCategory = $state('');

	// 대기 상태 상품 체크를 위한 derived 값들
	let isWaitingProduct = $derived(
		checkingProduct?.repair_product?.status === REPAIR_STATUS_WAITING
	);
	// 대기 상태 상품 재처리 여부 (한 번 대기 상태였던 상품은 무조건 complete로 처리)
	let isWaitingProductRecheck = $state(false);

	// 완료 버튼 제목 (대기 상태 상품 재처리 시 다르게 표시)
	let completeButtonTitle = $derived(
		isWaitingProductRecheck ? '대기 상품 완료 처리' : '점검(수리)완료'
	);

	// 서버에서 받아온 원본 구성품 목록 (변경하지 않음)
	let originalPartsList: SelectedPart[] = $state([]);
	// 화면에 표시할 전체 구성품 목록 (서버 데이터 + 새로 추가된 데이터)
	let selectedPartsList: SelectedPart[] = $state([]);
	// API 전송용 통합 구성품 리스트 (추가/수정/삭제 모두 포함)
	let partsChanges: Array<{
		id?: number; // 기존 구성품의 경우 repair_product_parts.id
		parts_id: number; // repair_parts.id
		quantity: number;
		price: number;
		action: 'add' | 'update' | 'delete'; // 작업 유형
	}> = $state([]);

	// 입력된 바코드가 QAID일 경우 서버에서 상품정보를 가져온다.
	async function getProductInfo() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/check-product/${checkingBarcode}`);
			if (status === 200 && data.success) {
				checkingProduct = data.data.product;
				repairGradeCodes = data.data.grades; // 수리 등급을 먼저 로드

				// 구성품 신청으로 대기중인 상품 이었다면...
				if (isWaitingProduct) {
					// 대기 상태 상품 재처리 플래그 설정
					isWaitingProductRecheck = true;

					// 대기 상태 상품인 경우 구성품 도착 확인 메시지 표시
					prodScanMessage =
						'구성품 대기 중인 상품입니다.<br>구성품이 도착했는지 확인 후 재처리를 진행하세요.';
					console.log('대기 상태 상품 재처리 시작:', checkingProduct.qaid);

					memo = checkingProduct.repair_product.memo;
					selectedGradeCode = checkingProduct?.repair_product.repair_grade.code;
					if (selectedGradeCode) {
						await getRepairSymptoms();

						showSymptomContent = true;
						selectedSymptomCode = checkingProduct?.repair_product.repair_symptom.code;
						if (selectedSymptomCode) {
							await getRepairProcesses();

							showProcessContent = true;
							selectedProcessCode = checkingProduct?.repair_product.repair_process.code;
						}

						// @todo: 구성품 시스템이 완성 될 때까지 사용 안 함
						// showPartsContentInternal = !isAppleProduct; // Apple 제품이 아닌 경우에만 구성품 선택 표시

						// 서버에서 받아온 구성품 목록 설정
						originalPartsList = checkingProduct.repair_product.repair_product_parts.map(
							(part: any) => ({
								id: part.id,
								product_id: part.repair_product_id,
								parts_id: part.repair_parts_id,
								name: part.repair_part?.name,
								price: part.price || 0,
								quantity: part.quantity || 1
							})
						);

						selectedPartsList = [...originalPartsList];

						// 구성품 변경사항 초기화 (기존 구성품들은 현재 상태로 설정)
						partsChanges = checkingProduct.repair_product.repair_product_parts.map((part: any) => ({
							id: part.id, // repair_product_parts.id
							parts_id: part.repair_parts_id, // repair_parts.id
							quantity: part.quantity || 1,
							price: part.price || 0,
							action: 'update' as const // 기존 구성품은 업데이트로 처리
						}));
					}
				} else {
					// 새로운 상품이므로 재처리 플래그 초기화
					isWaitingProductRecheck = false;
					selectedGradeCode = '';
					selectedSymptomCode = '';
					selectedProcessCode = '';

					// 상품 정보를 가져 왔을 때 수리 등급만 보이게 설정
					showGradeContent = true;
					showSymptomContent = false;
					showProcessContent = false;
					showPartsContentInternal = false;

					// 새로운 상품이므로 구성품 관련 배열 초기화
					originalPartsList = [];
					selectedPartsList = [];
					partsChanges = [];
				}
			} else {
				console.log('상품 정보 조회 실패');
				checkingProduct = null;
				showGradeContent = false;
				showSymptomContent = false;
				showProcessContent = false;
				showPartsContentInternal = false;

				// showModal(data.data.message, 'error', '상품 정보 없음');
			}
			checkView();
		} catch (e: any) {
			clearValues();

			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '상품 정보 오류');
		}
	}

	// 선택된 수리 등급에 해당하는 증상 내용을 가져온다.
	async function getRepairSymptoms() {
		try {
			selectedSymptomCode = ''; // 선택된 수리 등급이 바뀌면 증상 내용도 초기화
			selectedProcessCode = ''; // 증상 내용도 초기화되므로 처리 내용도 초기화

			const grade = repairGradeCodes.find((grade) => grade.code === selectedGradeCode);
			if (!grade) {
				repairSymptomCodes = [];
				showProcessContent = false;
				return;
			}

			const { status, data } = await authClient.get(
				`${apiUrl}/code/symptoms-processes/grade/${grade.id}/${reqType}`
			);
			if (status === 200 && data.success) {
				repairSymptomCodes = data.data.symptoms;
				repairProcessCodes = data.data.processes;
				showProcessContent = false; // 증상 내용이 새로 로드되므로 처리 내용 숨김

				// 증상이 1개만 있다면 자동으로 선택
				if (repairSymptomCodes.length === 1) {
					selectedSymptomCode = repairSymptomCodes[0].code;
					console.log('증상이 1개만 있어서 자동 선택됨:', selectedSymptomCode);

					// 처리 내용도 1개만 있다면 자동으로 선택
					if (repairProcessCodes.length === 1) {
						showProcessContent = true;
						selectedProcessCode = repairProcessCodes[0].code;
						console.log('처리 내용이 1개만 있어서 자동 선택됨:', selectedProcessCode);
					}
				}
			}
		} catch (e: any) {
			showProcessContent = false;
			repairSymptomCodes = [];

			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '증상 내용 오류');
		}
	}

	// 선택된 증상에 해당하는 처리 내용을 가져온다.
	async function getRepairProcesses() {
		try {
			selectedProcessCode = ''; // 선택된 증상이 바뀌면 처리 내용도 초기화

			const symptom = repairSymptomCodes.find((symptom) => symptom.code === selectedSymptomCode);
			const grade = repairGradeCodes.find((grade) => grade.code === selectedGradeCode);

			if (!symptom || !grade) {
				repairProcessCodes = [];
				return;
			}

			const { status, data } = await authClient.get(
				`${apiUrl}/code/processes/symptom/${symptom?.id}/${grade.id}`
			);
			if (status === 200 && data.success) {
				repairProcessCodes = data.data.processes;

				// 처리 내용이 1개만 있다면 자동으로 선택
				if (repairProcessCodes.length === 1) {
					selectedProcessCode = repairProcessCodes[0].code;
					console.log('처리 내용이 1개만 있어서 자동 선택됨:', selectedProcessCode);
				}
			}
		} catch (e: any) {
			repairProcessCodes = [];

			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '처리 내용 오류');
		}
	}

	// 선택된 처리 내용에 해당하는 수리 등급을 가져온다.
	async function getRepairGrades() {
		try {
			// 초기 로딩 시 모든 수리 등급을 가져옴
			if (!selectedProcessCode) {
				const { status, data } = await authClient.get(`${apiUrl}/code/grades`);
				if (status === 200 && data.success) {
					repairGradeCodes = data.data.grades;
				}
				return;
			}

			// 대기 상태 상품 재처리 시 RP_WAITING 선택 방지
			if (isWaitingProductRecheck && selectedProcessCode === 'RP_WAITING') {
				showModal(
					'대기 상태 상품 재처리 시에는 대기 처리를 선택할 수 없습니다. 완료 처리로 진행해 주세요.',
					'warning',
					'처리 내용 오류'
				);
				console.log('대기 상태 상품 재처리 - 강제로 완료 처리로 변경');
			}

			// @todo: 구성품 시스템이 완성 될 때까지 사용 안 함
			// Apple 제품이 아닌 경우에만 구성품 관련 UI 표시
			// showPartsContentInternal = !isAppleProduct;

			// Apple 제품이거나 가공불가 처리 시 모든 구성품 삭제
			if (isAppleProduct || selectedProcessCode === 'RP_ABANDON') {
				selectedPartsList = [];
				// 기존 구성품이 있다면 모두 삭제 상태로 변경
				if (originalPartsList.length > 0) {
					partsChanges = originalPartsList.map((p) => ({
						id: p.id,
						parts_id: p.parts_id,
						quantity: 0,
						price: p.price,
						action: 'delete' as const
					}));
				} else {
					partsChanges = [];
				}
			}

			if (selectedProcessCode === 'RP_WAITING') {
				completeButtonTitle = '구성품 (청구)대기';
			} else if (selectedProcessCode === 'RP_COMPO') {
				completeButtonTitle = '점검(수리)완료';
			}

			// 수리 등급은 이미 선택되어 있으므로 추가 API 호출 불필요
			console.log('수리 등급이 이미 선택되어 있습니다:', selectedGradeCode);
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '수리 등급 오류');
		}
	}

	async function getPartsCategories() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/parts-categories`);
			if (status === 200 && data.success) {
				repairPartsCategories = data.data.categories;
			}
		} catch (e) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '구성품 카테고리 오류');
		}
	}

	async function getParts() {
		if (selectedPartsCategory === '') {
			return;
		}

		try {
			const { status, data } = await authClient.get(`${apiUrl}/parts/${selectedPartsCategory}`);
			if (status === 200 && data.success) {
				repairParts = data.data.parts;
			}
		} catch (e) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '구성품 정보 오류');
		}
	}

	// 구성품 추가
	function handleAddParts() {
		if (selectedParts === '') {
			showModal('구성품을 선택해 주세요.', 'warning', '구성품 오류');
			return;
		}

		// 선택된 구성품 정보 찾기
		const selectedPartId = Number(selectedParts);
		const selectedPart = repairParts.find((p) => p.id === selectedPartId);

		if (!selectedPart) {
			showModal('선택된 구성품이 없습니다.', 'warning', '구성품 오류');
			return;
		}

		if (selectedPart.is_purchasable === 'N') {
			showModal(errMessages.not_purchasable, 'error', '구성품 구매 불가');

			// 가공불가로 처리
			selectedProcessCode = 'RP_ABANDON';
			getRepairGrades();

			// 변수 초기화
			selectedParts = '';
			selectedPartsCategory = '';
			repairParts = [];
			return;
		}

		// 대기 상태 상품 재처리 로직
		if (isWaitingProduct) {
			console.log('대기 상태 상품에 구성품 추가:', selectedPart.name);
			prodScanMessage = `대기 상품에 구성품이 추가되었습니다: ${selectedPart.name}`;
		}

		// 이미 존재하는 구성품인지 확인 (parts_id 기준)
		const existingPartIndex = selectedPartsList.findIndex(
			(p) => Number(p.parts_id) === selectedPartId
		);

		if (existingPartIndex !== -1) {
			// 기존 구성품의 수량 증가
			selectedPartsList[existingPartIndex].quantity += 1;

			// 변경사항 업데이트
			const existingChangeIndex = partsChanges.findIndex(
				(p) => Number(p.parts_id) === selectedPartId
			);
			if (existingChangeIndex !== -1) {
				partsChanges[existingChangeIndex].quantity += 1;
			}

			// 반응성을 위해 배열 재할당
			selectedPartsList = [...selectedPartsList];
			partsChanges = [...partsChanges];

			console.log(`${selectedPart.name}의 수량이 증가되었습니다.`);
		} else {
			// 새로운 구성품 추가
			const newPartId = Date.now(); // 임시 ID (화면 표시용)

			selectedPartsList = [
				...selectedPartsList,
				{
					id: newPartId,
					product_id: checkingProduct.id,
					parts_id: selectedPartId,
					name: selectedPart.name,
					price: selectedPart.price,
					quantity: 1
				}
			];

			partsChanges = [
				...partsChanges,
				{
					parts_id: selectedPartId,
					quantity: 1,
					price: selectedPart.price,
					action: 'add'
				}
			];

			console.log(`${selectedPart.name}이 추가되었습니다.`);
		}
		console.log('partsChanges: ', partsChanges);

		// 선택 초기화
		selectedParts = '';
		selectedPartsCategory = '';
		repairParts = [];
	}

	// 구성품 수량 증가 함수
	function handleIncreaseQuantity(partId: number) {
		const partIndex = selectedPartsList.findIndex((p) => p.id === Number(partId));
		if (partIndex === -1) return;

		const part = selectedPartsList[partIndex];

		// 화면 표시용 수량 증가
		selectedPartsList[partIndex].quantity += 1;

		// 변경사항 업데이트 (parts_id 기준으로 찾기)
		const changeIndex = partsChanges.findIndex((p) => Number(p.parts_id) === Number(part.parts_id));
		if (changeIndex !== -1) {
			partsChanges[changeIndex].quantity += 1;
		}

		// 반응성을 위해 배열 재할당
		selectedPartsList = [...selectedPartsList];
		partsChanges = [...partsChanges];

		console.log(`${part.name}의 수량이 증가되었습니다.`);
		console.log('partsChanges: ', partsChanges);
	}

	// 구성품 삭제 함수
	function handleRemoveParts(partId: number) {
		const partIndex = selectedPartsList.findIndex((p) => p.id === partId);
		if (partIndex === -1) return;

		const part = selectedPartsList[partIndex];

		if (part.quantity > 1) {
			// 수량이 1보다 크면 수량만 감소
			selectedPartsList[partIndex].quantity -= 1;

			// 변경사항 업데이트 (parts_id 기준으로 찾기)
			const changeIndex = partsChanges.findIndex(
				(p) => Number(p.parts_id) === Number(part.parts_id)
			);
			if (changeIndex !== -1) {
				partsChanges[changeIndex].quantity -= 1;
			}

			// 반응성을 위해 배열 재할당
			selectedPartsList = [...selectedPartsList];
			partsChanges = [...partsChanges];

			console.log(`${part.name}의 수량이 감소되었습니다.`);
		} else {
			// 수량이 1이면 완전히 제거
			const isOriginalPart = originalPartsList.some((p) => p.parts_id === part.parts_id);

			if (isOriginalPart) {
				// 기존 구성품인 경우 삭제 표시
				const changeIndex = partsChanges.findIndex(
					(p) => Number(p.parts_id) === Number(part.parts_id)
				);
				if (changeIndex !== -1) {
					partsChanges[changeIndex].action = 'delete';
					partsChanges[changeIndex].quantity = 0;
				}
			} else {
				// 새로 추가된 구성품인 경우 변경사항에서 제거
				partsChanges = partsChanges.filter((p) => Number(p.parts_id) !== Number(part.parts_id));
			}

			// 화면에서 제거
			selectedPartsList = selectedPartsList.filter((p) => p.id !== partId);

			console.log(`${part.name}이 삭제되었습니다.`);
		}

		console.log('partsChanges: ', partsChanges);
	}

	// 핸들러 변수 초기화
	function clearHandler1() {
		selectedGradeCode = '';
		selectedSymptomCode = '';
		selectedProcessCode = '';
		showGradeContent = false;
		showSymptomContent = false;
		showProcessContent = false;
		showPartsContent = false;
	}

	function clearHandler2() {
		selectedSymptomCode = '';
		selectedProcessCode = '';
		showSymptomContent = false;
		showProcessContent = false;
		showPartsContent = false;
	}

	function clearHandler3() {
		selectedProcessCode = '';
		showProcessContent = false;
		showPartsContent = false;
	}

	function selectProcess(process_command: string) {
		prodScanMessage = '';

		const [process_type, process_code] = process_command.split('/');

		const handler = processHandlers[process_type as keyof typeof processHandlers];
		if (handler) {
			handler(process_code);
		} else {
			console.error('Unknown process type:', process_type);
		}
	}

	// 수리 작업 처리 핸들러
	const processHandlers = {
		grade: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					checkingProduct = null;
					clearHandler1();
					return;
				}
			}

			selectedGradeCode = process_code;
			checkingBarcode = '';
			// 수리 등급이 선택되면 증상 내용 표시
			showSymptomContent = true;
			showProcessContent = false;
			showPartsContent = false;
			getRepairSymptoms(); // 수리 등급에 따른 증상 목록 가져오기
		},

		check: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					checkingProduct = null;
					clearHandler1();
					return;
				}
			}

			if (!selectedGradeCode && handleError('수리 등급을 먼저 선택해 주세요.')) {
				clearHandler2();
				return;
			}

			selectedSymptomCode = process_code;
			checkingBarcode = '';
			// 증상 내용이 선택되면 처리 내용 표시
			showProcessContent = true;
			showGradeContent = false;
			showPartsContent = false;
			getRepairProcesses();
		},

		repair: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					clearHandler1();
					return;
				}
			}

			if (!selectedGradeCode && handleError('수리 등급을 먼저 선택해 주세요.')) {
				clearHandler2();
				return;
			}

			if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
				clearHandler3();
				return;
			}

			selectedProcessCode = process_code;
			checkingBarcode = '';
			// @todo: 구성품 시스템이 완성 될 때까지 사용 안 함
			// // 처리 내용이 선택되면 구성품 표시 (Apple 제품이 아닌 경우에만)
			// if (selectedProcessCode === 'RP_COMPO') {
			// 	showPartsContentInternal = !isAppleProduct;
			// }
			getRepairGrades();
		}
	};

	async function completeCheckIn() {
		const product_id = checkingProduct?.id;
		const repair_product_id = checkingProduct?.repair_product?.id ?? null;
		const qaid = checkingProduct?.qaid;

		if (
			typeof product_id !== 'undefined' &&
			selectedGradeCode !== '' &&
			selectedSymptomCode !== '' &&
			selectedProcessCode !== ''
		) {
			// 대기 상태 상품 재처리 시 RP_WAITING 선택 방지
			if (isWaitingProductRecheck && selectedProcessCode === 'RP_WAITING') {
				showModal(
					'대기 상태 상품 재처리 시에는 대기 처리를 선택할 수 없습니다. 완료 처리로 진행해 주세요.',
					'warning',
					'처리 내용 오류'
				);
				checkView();
				return;
			}
			// 대기 상태 상품 재처리인 경우 무조건 complete로 처리
			if (isWaitingProductRecheck) {
				repairStatus = 'complete';
				console.log('대기 상태 상품 재처리 - 무조건 완료 상태로 설정:', qaid);
				prodScanMessage = '대기 상태 상품이 완료 처리 됩니다.';
			} else {
				// 새로운 상품의 경우 처리 내용에 따라 상태 결정
				if (selectedProcessCode === 'RP_WAITING') {
					repairStatus = 'waiting';
					console.log('상품이 구성품 대기 상태로 설정됩니다:', qaid);
				} else {
					repairStatus = 'complete';
				}
			}

			// 서버 전송용 데이터 분리
			const addParts = partsChanges
				.filter((p) => p.action === 'add')
				.map((p) => ({
					parts_id: p.parts_id,
					quantity: p.quantity,
					price: p.price
				}));

			const updateParts = partsChanges
				.filter((p) => p.action === 'update')
				.map((p) => ({
					id: p.id, // repair_product_parts.id
					parts_id: p.parts_id,
					quantity: p.quantity,
					price: p.price
				}));

			const removeParts = partsChanges
				.filter((p) => p.action === 'delete')
				.map((p) => p.id)
				.filter((id) => id !== undefined);

			let payload = {
				repair_product_id,
				product_id: product_id,
				qaid: qaid,
				status: repairStatus,
				symptom_code: selectedSymptomCode,
				process_code: selectedProcessCode,
				grade_code: selectedGradeCode,
				os_reinstall: osReinstall,
				add_parts: addParts,
				update_parts: updateParts,
				remove_parts: removeParts,
				memo: memo
			};
			console.log(payload);

			await playAudio(AudioEvent.COMPLETING_REPAIR); // 오디오: 검수완료 합니다.

			try {
				const { status, data } = await authClient.post(`${apiUrl}/store`, payload);
				if (status === 200 && data.success) {
					clearValues();
					// 작업 완료 후 작업 내역 즉시 갱신
					await refreshWorkHistory();
				} else {
					showModal(data.data.message, 'error', '저장 실패');
					await playAudio(AudioEvent.FAIL_AND_RETRY); // 오디오: 실패하였습니다. 다시 진행해 주시기 바랍니다.
				}
			} catch (e: any) {
				await playAudio(AudioEvent.FAIL_AND_RETRY); // 오디오: 실패하였습니다. 다시 진행해 주시기 바랍니다.

				const message = (await handleCatch(e, true)) as string;
				showModal(message, 'error', '상품 정보 오류');
			}
		} else {
			showModal(errMessages.check_all_process, 'warning', '필수 항목 확인');
			checkView();
			return;
		}
	}

	async function cancelCheckIn() {
		clearValues();
	}

	// 주요변수 모두 초기화
	function clearValues() {
		checkingProduct = null;
		selectedGradeCode = '';
		selectedSymptomCode = '';
		selectedProcessCode = '';
		memo = '';
		prodScanMessage = '';
		osReinstall = false;
		selectedPartsList = [];
		originalPartsList = [];
		partsChanges = [];
		isWaitingProductRecheck = false;

		// 순차적 표시 상태 초기화 (새로운 순서)
		showGradeContent = false;
		showSymptomContent = false;
		showProcessContent = false;
		showPartsContentInternal = false;

		isCompleteButtonSuccess = false;
		completeButtonTitle = '점검(수리)완료';
	}

	$effect(() => {
		preventKoreanInput(checkingBarcode, (newValue) => {
			checkingBarcode = newValue;
		});
	});

	onMount(async () => {
		// 오디오 시스템 초기화
		initAudio({
			enabled: true,
			volume: 1.0,
			preloadAll: true
		});

		await getPartsCategories();
		setGuestUser();
		checkView();
	});

	const breadcrumbs: Breadcrumb[] = [{ title: '수리/점검', url: '/works/repairs' }];
</script>

<svelte:head>
	<title>수리/점검</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full flex flex-row justify-start gap-4">
					<div class="w-1/2 flex flex-col">
						<div class="w-full">
							<!--바코드 스캔-->
							<div class="scan-container">
								<div class="scan-header">
									<div class="flex items-center gap-3">
										<Icon data={faBarcode} class="text-xl" />
										<span class="text-xl font-bold">바코드 스캔</span>
									</div>
								</div>

								<div class="scan-content">
									<!-- 스캔 메시지 -->
									{#if prodScanMessage}
										<ScanMessage
											message={prodScanMessage}
											type="error"
											show={true}
											className="mt-3"
										/>
									{:else}
										<ScanMessage
											message="바코드값을 입력(스캔)해 주시기를 바랍니다."
											type="info"
											show={true}
										/>
									{/if}

									<!-- 바코드 입력 -->
									<div class="scan-input-group">
										<input
											bind:this={focusCheckingBarcode}
											bind:value={checkingBarcode}
											class="scan-input"
											onkeydown={async (e) => {
												if (e.key === 'Enter') {
													await handleBarcodeInput(e);
												}
											}}
											placeholder="바코드 입력 또는 스캔"
											type="text"
										/>

										<button
											class="scan-button"
											onclick={async (e) => await handleBarcodeInput(e)}
											type="button"
										>
											확인
										</button>
									</div>

									<!-- 상품 정보 -->
									{#if checkingProduct && checkingProduct.id}
										<div class="product-info">
											<!-- 대기 상태 상품 표시 -->
											{#if isWaitingProduct}
												<div class="waiting-product-alert">
													<div class="waiting-alert-content">
														<Icon data={faBoxesPacking} class="waiting-icon" />
														<span class="waiting-text">구성품 대기 중인 상품</span>
													</div>
													<div class="waiting-description">
														구성품이 도착했는지 확인 후 재처리를 진행하세요.
														{#if isWaitingProductRecheck}
															<br /><strong class="text-red-600"
																>※ 재처리 시 무조건 완료 상태로 처리됩니다.</strong
															>
														{/if}
													</div>
												</div>
											{/if}

											<div class="product-info-row">
												<div class="product-info-label">QAID</div>
												<div class="product-info-value">
													{#if checkingProduct.qaid}
														<span class="font-mono">{checkingProduct.qaid}</span>

														{#if checkingProduct.rg === 'Y'}
															<Icon data={faRocket} scale={1.5} class="ml-2 text-red-500" />
														{/if}

														{#if showRestrictedArea}
															<button
																class="ml-2 p-1 text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200"
																onclick={async () =>
																	await isRePrint(checkingProduct?.qaid, checkingProduct?.id)}
																title="QAID 재발행"
															>
																<Icon data={faPrint} />
															</button>
														{/if}
													{/if}
												</div>
											</div>

											<div class="product-info-row">
												<div class="product-info-label">상품명</div>
												<div class="product-info-value">
													<div class="inline-flex items-center gap-1 leading-none">
														<span>
															{checkingProduct.name}
															{#if checkingProduct.link?.product_id && checkingProduct.link.product_id.slice(-8) !== '00000000'}
																<a
																	href="https://www.coupang.com/"
																	class="text-error"
																	aria-label="쿠팡 상품 페이지 열기"
																	title="쿠팡 상품 페이지 열기"
																	onclick={(e) => {
																		e.preventDefault();
																		const id = generateRandomNumber();
																		const label = `coupang-${id}`;
																		const url = `https://www.coupang.com/vp/products/${checkingProduct.link?.product_id}?itemId=${checkingProduct.link?.item_id}&vendorItemId=${checkingProduct.link?.vendor_item_id}`;
																		openWebviewWindow(url, label, { width: 1280, height: 1024 });
																	}}
																>
																	<Icon data={faCopyright} />
																</a>
															{/if}

															{#if checkingProduct?.return_reason !== null}
																<span
																	class="tooltip tooltip-info tooltip-bottom"
																	data-tip={formattedReturnReason(
																		checkingProduct?.return_reason?.reason
																	)}
																>
																	<Icon data={faQuestion} class="text-primary-700 cursor-help" />
																</span>
															{/if}
														</span>
													</div>
												</div>
											</div>

											{#if showGradeContent && repairGradeCodes}
												<div class="product-info-row">
													<div class="product-info-label">수리 상태</div>
													<div class="product-info-value">
														<select
															bind:value={selectedGradeCode}
															class="product-info-select"
															onchange={async () => {
																if (selectedGradeCode) showSymptomContent = true;
																await getRepairSymptoms();
															}}
														>
															<option value="">선택</option>
															{#if repairGradeCodes}
																{#each repairGradeCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>
											{/if}

											{#if showSymptomContent && repairSymptomCodes}
												<div class="product-info-row">
													<div class="product-info-label">증상 내용</div>
													<div class="product-info-value">
														<select
															bind:value={selectedSymptomCode}
															class="product-info-select"
															onchange={async () => {
																if (selectedSymptomCode) showProcessContent = true;
																await getRepairProcesses();
															}}
														>
															<option value="">선택</option>
															{#if repairSymptomCodes}
																{#each repairSymptomCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>
											{/if}

											{#if showProcessContent && repairProcessCodes}
												<div class="product-info-row">
													<div class="product-info-label">처리 내용</div>
													<div class="product-info-value">
														<select
															bind:value={selectedProcessCode}
															class="product-info-select w-2/3"
															onchange={async () => {
																if (selectedProcessCode) {
																	showGradeContent = true;
																	// @todo: 구성품 시스템이 완성 될 때까지 사용 안 함
																	// showPartsContentInternal = selectedProcessCode === 'RP_COMPO';
																}
																await getRepairGrades();
															}}
														>
															<option value="">선택</option>
															{#if repairProcessCodes}
																{#each repairProcessCodes as item (item.code)}
																	{#if user.role.toLowerCase() === 'guest'}
																		{#if item.code !== 'RP_WAITING'}
																			<option
																				value={item.code}
																				disabled={isWaitingProductRecheck &&
																					item.code === 'RP_WAITING'}
																			>
																				{item.name}
																				{#if isWaitingProductRecheck && item.code === 'RP_WAITING'}
																					(재처리 시 선택 불가)
																				{/if}
																			</option>
																		{/if}
																	{:else}
																		<option
																			value={item.code}
																			disabled={isWaitingProductRecheck &&
																				item.code === 'RP_WAITING'}
																		>
																			{item.name}
																			{#if isWaitingProductRecheck && item.code === 'RP_WAITING'}
																				(재처리 시 선택 불가)
																			{/if}
																		</option>
																	{/if}
																{/each}
															{/if}
														</select>

														{#if isOsReinstallAllowed && (isComputerCategory || isTabletCategory)}
															<div class="checkbox-container ml-4 whitespace-nowrap">
																<input
																	bind:checked={osReinstall}
																	class="checkbox-input"
																	type="checkbox"
																/>
																<label class="checkbox-label">OS 재설치</label>
															</div>
														{/if}
													</div>
												</div>
											{/if}

											{#if showPartsContent}
												<div class="product-info-row">
													<div class="product-info-label">구성품 추가</div>
													<div class="product-info-value">
														<div class="parts-controls">
															<div class="flex gap-2 mb-3">
																<select
																	bind:value={selectedPartsCategory}
																	class="product-info-select flex-1"
																	onchange={async () => {
																		await getParts();
																	}}
																>
																	<option value="">분류 선택</option>
																	{#if repairPartsCategories}
																		{#each repairPartsCategories as item (item.id)}
																			<option value={item.id}>{item.name}</option>
																		{/each}
																	{/if}
																</select>

																<select
																	bind:value={selectedParts}
																	class="product-info-select flex-1"
																	disabled={selectedPartsCategory === ''}
																>
																	<option value="">구성품 선택</option>
																	{#if repairParts}
																		{#each repairParts as item (item.id)}
																			<option value={item.id}>{item.name}</option>
																		{/each}
																	{/if}
																</select>

																<button
																	class="parts-add-button"
																	onclick={handleAddParts}
																	type="button"
																>
																	추가
																</button>
															</div>

															{#if selectedPartsList.length > 0}
																<div class="parts-list">
																	{#each selectedPartsList as part (part.id)}
																		<div class="parts-item">
																			<div class="flex-1">
																				<span class="font-medium">{part.name}</span>
																				<span class="badge badge-primary badge-sm ml-2">
																					수량: {part.quantity}
																				</span>
																			</div>
																			<div class="flex items-center space-x-2">
																				<!-- 수량 증가 버튼 -->
																				<button
																					class="btn btn-sm btn-ghost"
																					onclick={() => handleIncreaseQuantity(part.id)}
																				>
																					<Icon data={faPlus} />
																				</button>

																				<!-- 수량 감소/삭제 버튼 -->
																				<button
																					class="btn btn-sm btn-error"
																					onclick={() => handleRemoveParts(part.id)}
																				>
																					{#if part.quantity > 1}
																						<Icon data={faMinus} />
																					{:else}
																						<Icon data={faTrash} />
																					{/if}
																				</button>
																			</div>
																		</div>
																	{/each}
																</div>
															{:else}
																<div class="parts-empty">선택된 구성품이 없습니다.</div>
															{/if}
														</div>
													</div>
												</div>
											{/if}

											<div class="product-info-row">
												<div class="product-info-label">메모</div>
												<div class="product-info-value">
													<textarea
														bind:value={memo}
														class="product-info-textarea"
														rows="3"
														placeholder="작성내용은 처리내용 뒤에 첨부됩니다"
													></textarea>
												</div>
											</div>
										</div>

										<!-- 액션 버튼 -->
										<div class="action-buttons">
											<button
												class="action-button"
												class:action-button-success={isCompleteButtonSuccess}
												class:action-button-default={!isCompleteButtonSuccess}
												onclick={completeCheckIn}
												type="button"
											>
												<Icon data={faBoxesPacking} class="mr-2" />
												{completeButtonTitle}
											</button>

											<button
												class="action-button action-button-neutral"
												onclick={cancelCheckIn}
												type="button"
											>
												<Icon data={faBan} class="mr-2" />
												취소
											</button>
										</div>
									{/if}
								</div>
							</div>
						</div>

						{#if showRestrictedArea}
							<ExampleBarcodeCommand {commandVisible} page="repair" />
						{/if}
					</div>
					<div class="w-1/2 flex flex-col">
						{#if user.role.toLowerCase() === 'guest'}
							<GuestSearch />
						{:else}
							<WorkHistoryList />
						{/if}
					</div>
				</div>
			</section>

			<MessageModal
				bind:this={modal}
				type={modalType}
				title={modalTitle}
				message={modalMessage}
				onClose={handleModalClose}
			/>
		</div>
	{/snippet}
</AppLayout>

<style>
	/* 대기 상태 상품 알림 스타일 */
	.waiting-product-alert {
		background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
		border: 2px solid #f59e0b;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 16px;
		box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);
	}

	.waiting-alert-content {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 4px;
	}

	.waiting-icon {
		color: #d97706;
		font-size: 1.2em;
	}

	.waiting-text {
		font-weight: 600;
		color: #92400e;
		font-size: 0.95em;
	}

	.waiting-description {
		color: #78350f;
		font-size: 0.85em;
		margin-left: 28px;
	}

	/* 대기 상태 상품 테이블 행 스타일 */
	.waiting-product-row {
		background-color: #fef3c7 !important;
		border-left: 4px solid #f59e0b;
	}

	.waiting-product-row:hover {
		background-color: #fde68a !important;
	}

	/* 대기 상태 배지 */
	.waiting-badge {
		background-color: #f59e0b;
		color: white;
		padding: 2px 6px;
		border-radius: 4px;
		font-size: 0.75em;
		font-weight: 600;
	}
</style>
