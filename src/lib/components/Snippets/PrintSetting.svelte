<script lang="ts">
	import { onMount, tick } from 'svelte';
	import { PrinterDatabase, type SettingItem } from '$lib/utils/PrinterDatabase';
	import { formatDate, testPrint } from '$lib/utils/BarcodeUtils';
	import { executeAsk, executeMessage } from '$lib/Functions';
	import { saveAs } from 'file-saver';
	import { toast } from 'svoast';

	const db = new PrinterDatabase();

	let settingNameElement: HTMLInputElement;
	
	const usePrintConfig = window.localStorage.getItem('usePrintConfig') ?? '기본(default)';
	let settingName = $state(usePrintConfig); // 선택된 설정 이름
	let settingNameList: string[] = $state([]);
	// 선택된 설정의 세부 설정 - 데이터베이스 구조에 맞게 수정
	let printSettings: SettingItem[] = $state([]);
	
	// 기본값으로 초기화
	let fontConfig = $state({ fontFamily: 'Roboto' });
	let barcodeConfig = $state({ x: 0, y: 3 });
	let qaidConfig = $state({ fontSize: 10 });
	let dateConfig = $state({ x: 71, y: 67, format: 'MM/DD/YY', fontSize: 6, textColor: '#000000', bold: true, italics: true });
	let userConfig = $state({ x: 120, y: 67, fontSize: 6, textColor: '#000000', bold: true, italics: true });
	
	async function loadSettings() {
		if (!settingName.trim()) {
			await executeMessage('새로운 설정의 이름을 입력해주세요.');
			
			settingNameElement.value = '';
			await tick();
			settingNameElement.focus();
			return;
		}

		try {
			const settings = await db.getSettings(settingName);
			await tick();

			if (!settings || !Array.isArray(settings)) {
				await executeMessage('프린트 설정을 찾을 수 없습니다.', 'error');
				return;
			}

			// config에서 특정 코드의 설정을 찾는 헬퍼 함수
			const getConfigSettings = (code: string, defaultValue: any) => {
				const configItem = settings.find((group) => group.code === code);
				return configItem?.settings || defaultValue;
			};

			fontConfig = getConfigSettings('font', { fontFamily: 'Roboto' });
			barcodeConfig = getConfigSettings('barcode', { x: 0, y: 3 });
			qaidConfig = getConfigSettings('qaid', { fontSize: 10 });
			dateConfig = getConfigSettings('date', { x: 71, y: 67, format: 'MM/DD/YY', fontSize: 6, textColor: '#000000', bold: true, italics: true });
			userConfig = getConfigSettings('user', { x: 120, y: 67, fontSize: 6, textColor: '#000000', bold: true, italics: true });
		} catch (error) {
			console.error(error);
			await executeMessage('설정을 불러오는데 실패했습니다.', 'error');
		}
	}

	async function handleSubmit() {
		if (!settingName.trim()) {
			await executeMessage('설정의 이름을 입력해주세요.');
			return;
		}

		try {
			// 각 config가 유효한 객체인지 확인하고 기본값으로 대체
			const safeConfigs = {
				font: fontConfig || { fontFamily: 'Roboto' },
				barcode: barcodeConfig || { x: 0, y: 3 },
				qaid: qaidConfig || { fontSize: 10 },
				date: dateConfig || { x: 71, y: 67, format: 'MM/DD/YY', fontSize: 6, textColor: '#000000', bold: true, italics: true },
				user: userConfig || { x: 120, y: 67, fontSize: 6, textColor: '#000000', bold: true, italics: true }
			};

			printSettings = [
				{ name: '폰트(글꼴)', code: 'font', settings: safeConfigs.font },
				{ name: '바코드', code: 'barcode', settings: safeConfigs.barcode },
				{ name: 'QAID', code: 'qaid', settings: safeConfigs.qaid },
				{ name: '출력일 형식', code: 'date', settings: safeConfigs.date },
				{ name: '번호', code: 'user', settings: safeConfigs.user }
			];
			await tick();

			await db.saveSettings(settingName, printSettings);

			// 새로운 설정이면 목록에 추가
			if (!settingNameList.includes(settingName)) {
				settingNameList = [...settingNameList, settingName];
			}
			await executeMessage("설정이 저장 되었습니다.");
		} catch (error: any) {
			await executeMessage('설정 저장에 실패했습니다' + error.message, 'error');
		}
	}

	async function handleDelete() {
		try {
			const answer = await executeAsk("정말 이 설정을 삭제하시겠습니까?");
			if (!answer) {
				return;
			}

			await db.deleteSettings(settingName);
			// 설정 목록 다시 불러오기
			settingNameList = await db.getAllSettingNames();
			// 선택된 설정을 새 설정으로 초기화
			settingName = "";
			// 기본값으로 설정 다시 로드
			await loadSettings();
			// 성공 메시지 표시
			await executeMessage('설정이 삭제되었습니다.');
		} catch (error: any) {
			await executeMessage('설정 삭제 중 오류가 발생했습니다: ' + error.message, 'error');
		}
	}

	// 백업 함수 추가
	async function handleBackup() {
		try {
			// 모든 설정 데이터 가져오기
			const allSettings: Record<string, any[]> = {};
			for (const name of settingNameList) {
				allSettings[name] = await db.getSettings(name);
			}

			// JSON 파일로 변환하여 다운로드
			const blob = new Blob([JSON.stringify(allSettings, null, 2)], {
				type: 'application/json'
			});
			console.log(blob, typeof JSON.stringify(allSettings, null, 2));
			// saveAs(blob, 'barcode-settings-backup.json');

			await executeMessage('설정이 백업되었습니다.');
		} catch (error: any) {
			await executeMessage('백업 중 오류가 발생했습니다: ' + error.message, 'error');
		}
	}

	// 복구 함수 추가
	async function handleRestore(event: Event) {
		try {
			const file = (event.target as HTMLInputElement).files?.[0];
			if (!file) return;

			const reader = new FileReader();
			reader.onload = async (e) => {
				try {
					const settings = JSON.parse(e.target?.result as string);

					// 각 설정을 IndexedDB에 저장
					for (const [name, data] of Object.entries(settings)) {
						await db.saveSettings(name, data as any[]);
					}

					// 설정 목록 새로고침
					settingNameList = await db.getAllSettingNames();
					await executeMessage('설정이 복구되었습니다.');
				} catch (error) {
					await executeMessage('설정 파일 복구 중 오류가 발생했습니다.', 'error');
				}
			};
			reader.readAsText(file);
		} catch (error: any) {
			await executeMessage('파일 읽기 중 오류가 발생했습니다: ' + error.message, 'error');
		}
	}

	onMount(async () => {
		await db.initDatabase();
		settingNameList = await db.getAllSettingNames();

		// localStorage에서 가져온 설정이 실제로 존재하는지 확인
		if (settingName && settingNameList.includes(settingName)) {
			await loadSettings();
		} else {
			// 존재하지 않는 설정이면 기본값으로 초기화
			settingName = '';
			await loadSettings();
		}
	});
</script>

<div class="w-1/2 flex flex-col">
	<div class="w-full mb-4">
		<p class="text-xl font-bold mb-2">
			프린터 출력 설정
		</p>
		
		<div class="form-control w-full">
			<div class="w-full flex gap-2 items-center">
				<span class="font-bold">🔸설정 선택: </span>
				<select
					bind:value={settingName}
					class="select select-bordered select-sm"
					onchange={loadSettings}>
					<option value="">새 설정</option>
					{#each settingNameList as name}
						<option value={name}>{name}</option>
					{/each}
				</select>
				{#if !settingNameList.includes(settingName)}
					<input
						type="text"
						placeholder="새 설정 이름 입력"
						class="input input-bordered input-sm w-40"
						bind:value={settingName}
						bind:this={settingNameElement}
					/>
				{/if}
				
				<!-- 백업/복구 버튼 추가 -->
				<button class="btn btn-sm btn-info" onclick={() => {
					window.localStorage.setItem('usePrintConfig', settingName);
					toast.success('선택한 설정을 사용합니다.');
				}}>사용</button>
				<button class="btn btn-sm btn-primary" onclick={handleBackup}>백업</button>
				<label class="btn btn-sm btn-secondary">
					복구
					<input accept=".json" onchange={handleRestore} style="display: none;" type="file" />
				</label>
				{#if settingNameList.includes(settingName) && settingName !== "기본(default)"}
					<button class="btn btn-sm btn-error" onclick={handleDelete}>삭제</button>
				{/if}
			</div>
		</div>
	</div>
	
	{#if printSettings}
		<div role="alert" class="alert alert-info mb-3">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				class="h-6 w-6 shrink-0 stroke-current">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
			</svg>
			<span>
				<ul class="list-disc pl-6">
					<li>설정을 선택한 후 <u>사용 버튼</u>을 눌러주세요.</li>
					<li>설정을 새로 만들거나 수정했다면 아래에 있는 <u>저장 버튼</u>을 클릭해 꼭 저장해 주세요. (저장 후 사용하기 위해서는 <u>사용 버튼</u>을 눌러 주세요).</li>
					<li>하단 <u>저장 버튼</u>을 누르지 않으면 설정이 사라집니다.</li>
				</ul>
			</span>
		</div>
		
		<div class="divider"></div>
		
		<div class="bg-white rounded-xl shadow-md p-6 space-y-8">
			<!-- 바코드 -->
			<section>
				<h2 class="text-lg font-semibold mb-2">바코드</h2>
				<div class="space-y-2">
					<div>
						바코드가 생성되는 부분으로 라벨 좌측 상단을 (0,0)으로 생각하고 위치를 조정해야 합니다.
					</div>
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">X축</label>
						<input type="number" bind:value={barcodeConfig.x} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300"/>
						<span class="text-xs text-gray-400 ml-2">가로 위치(px), 좌측으로 부터의 거리, 라벨지 좌측 상단이 0</span>
					</div>
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">Y축</label>
						<input type="number" bind:value={barcodeConfig.y} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300"/>
						<span class="text-xs text-gray-400 ml-2">세로 위치(px), 상단으로 부터의 높이, 라벨지 좌측 상단이 0</span>
					</div>
				</div>
			</section>

			<hr/>

			<!-- QAID -->
			<section>
				<h2 class="text-lg font-semibold mb-2">QAID</h2>
				<div class="flex items-center gap-2">
					<label class="w-16 text-sm text-gray-600">폰트크기</label>
					<input type="number" bind:value={qaidConfig.fontSize} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="7" max="20"/>
					<span class="text-xs text-gray-400 ml-2">텍스트 크기(px), 바코드 아래 QAID의 크기를 조정합니다. (최소 7, 최대 20)</span>
				</div>
			</section>

			<hr/>

			<!-- 출력일 형식 -->
			<section>
				<h2 class="text-lg font-semibold mb-2">출력일 형식</h2>
				<div class="flex items-center gap-2 mb-2">
					<input type="checkbox" bind:checked={dateConfig.bold} class="accent-blue-500"/>
					<span class="text-sm ml-2">두껍게</span>
				</div>
				<div class="flex items-center gap-6 mb-2">
					<label class="inline-flex items-center">
						<input type="radio" value="MM/DD/YY" bind:group={dateConfig.format} class="accent-blue-500"/>
						<span class="ml-2 text-sm">{formatDate(new Date(), 'MM/DD/YY')}</span>
					</label>
					<label class="inline-flex items-center">
						<input type="radio" value="YY-MM-DD" bind:group={dateConfig.format} class="accent-blue-500"/>
						<span class="ml-2 text-sm">{formatDate(new Date(), 'YY-MM-DD')}</span>
					</label>
				</div>
				<div class="space-y-2">
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">폰트크기</label>
						<input type="number" bind:value={dateConfig.fontSize} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="5" max="10"/>
						<span class="text-xs text-gray-400 ml-2">텍스트 크기(px), 출력일의 크기를 조정합니다. (최소 5, 최대 10)</span>
					</div>
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">X축</label>
						<input type="number" bind:value={dateConfig.x} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="0" max="130"/>
						<span class="text-xs text-gray-400 ml-2">가로 위치(px), 라벨지 좌측 상단이 0. (최대 130)</span>
					</div>
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">Y축</label>
						<input type="number" bind:value={dateConfig.y} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="0" max="70"/>
						<span class="text-xs text-gray-400 ml-2">세로 위치(px), 라벨지 좌측 상단이 0. (최대 70)</span>
					</div>
				</div>
			</section>

			<hr/>

			<!-- 번호 -->
			<section>
				<h2 class="text-lg font-semibold mb-2">번호</h2>
				<div class="flex items-center gap-2 mb-2">
					<input type="checkbox" bind:checked={userConfig.bold} class="accent-blue-500"/>
					<span class="text-sm ml-2">두껍게</span>
				</div>
				<div class="space-y-2">
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">폰트크기</label>
						<input type="number" bind:value={userConfig.fontSize} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="5" max="10"/>
						<span class="text-xs text-gray-400 ml-2">텍스트 크기(px), 번호의 크기를 조정합니다. (최소 5, 최대 10)</span>
					</div>
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">X축</label>
						<input type="number" bind:value={userConfig.x} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="0" max="130"/>
						<span class="text-xs text-gray-400 ml-2">가로 위치(px), 출력일 옆에 나와야 하므로 출력일의 X축보다 조금 더 큰 숫자로 설정해야 합니다. (최대 130)</span>
					</div>
					<div class="flex items-center gap-2">
						<label class="w-16 text-sm text-gray-600">Y축</label>
						<input type="number" bind:value={userConfig.y} class="w-24 px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300" min="0" max="70"/>
						<span class="text-xs text-gray-400 ml-2">세로 위치(px), 출력일 옆에 나와야 하므로 출력일의 Y축과 같은 위치로 설정해야 합니다. (최대 70)</span>
					</div>
				</div>
			</section>
		</div>
		
		<div class="w-full flex justify-center">
			<span class="mr-4">
				<button class="btn btn-info"
								onclick={async () => await testPrint()}
								type="button"
				>
					테스트 인쇄
				</button>
			</span>
			
			<span>
				<button class="btn btn-primary"
								onclick={async () => {
									await handleSubmit();
								}}
								type="button"
				>
					저장
				</button>
			</span>
		</div>
	{:else}
		<div>바코드 설정을 불러오는 중입니다...</div>
	{/if}

</div>